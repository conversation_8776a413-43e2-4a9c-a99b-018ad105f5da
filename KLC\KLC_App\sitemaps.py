from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from django.conf import settings
from datetime import datetime


class StaticViewSitemap(Sitemap):
    """Sitemap for static pages that don't change frequently"""
    priority = 0.5
    changefreq = 'monthly'
    protocol = 'https'

    def items(self):
        return [
            'welcome',
            'index',
            'check_person_id',
            'services',
            'about_developers',
            'admin_login'
        ]

    def location(self, item):
        return reverse(item)

    def lastmod(self, item):
        return datetime.now()

    def priority(self, item):
        priorities = {
            'welcome': 1.0,
            'index': 0.9,
            'services': 0.9,
            'check_person_id': 0.8,
            'about_developers': 0.3,
            'admin_login': 0.2,
        }
        return priorities.get(item, 0.5)

    def changefreq(self, item):
        frequencies = {
            'welcome': 'weekly',
            'index': 'weekly',
            'services': 'weekly',
            'check_person_id': 'monthly',
            'about_developers': 'yearly',
            'admin_login': 'yearly',
        }
        return frequencies.get(item, 'monthly')


class ServicesSitemap(Sitemap):
    """Sitemap for service pages"""
    priority = 0.7
    changefreq = 'monthly'
    protocol = 'https'

    def items(self):
        return [
            'reserve_hall',
            'make_transaction',
            'make_suggestions_complaints'
        ]

    def location(self, item):
        return reverse(item)

    def lastmod(self, item):
        return datetime.now()


class NewsSitemap(Sitemap):
    """Sitemap for news pages"""
    priority = 0.8
    changefreq = 'daily'
    protocol = 'https'

    def items(self):
        # Get news from Firestore
        try:
            db = settings.DB
            news_ref = db.collection('news_achievements').stream()
            news_items = []

            for doc in news_ref:
                data = doc.to_dict()
                news_items.append({
                    'id': doc.id,
                    'published_at': data.get('published_at', '')
                })

            # Add the news list page
            news_items.append({'id': 'list', 'published_at': ''})
            return news_items
        except:
            # Fallback if database is not available
            return [{'id': 'list', 'published_at': ''}]

    def location(self, item):
        if item['id'] == 'list':
            return reverse('news_list')
        else:
            return reverse('news_detail', args=[item['id']])

    def lastmod(self, item):
        if item['published_at']:
            try:
                return datetime.strptime(item['published_at'], '%Y-%m-%d')
            except:
                pass
        return datetime.now()

    def priority(self, item):
        if item['id'] == 'list':
            return 0.8
        return 0.6


# Sitemap dictionary for Django
sitemaps = {
    'static': StaticViewSitemap,
    'services': ServicesSitemap,
    'news': NewsSitemap,
}