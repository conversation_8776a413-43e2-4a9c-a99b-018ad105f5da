import bcrypt
import uuid
import os
import mimetypes
from django.shortcuts import render, redirect, get_object_or_404
from KLC_App.utils import import_excel_to_db, check_date_slot, upload_to_firebase_storage, delete_from_firebase_storage
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse, HttpResponse, FileResponse
from django.conf import settings
from datetime import datetime
from threading import Timer
from pytz import timezone
from firebase_admin import firestore
from django.contrib.auth.decorators import login_required
# from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from .forms import UploadFileForm, EditUserForm, AddUserForm, NewsForm, BulkDebtUpdateForm
import pandas as pd
from io import BytesIO
from django.core.files.storage import default_storage




db = settings.DB
# Initialize in-memory cache dictionaries
in_memory_cache_persons = {}
in_memory_cache_reservations = {}
in_memory_cache_transactions = {}

# Cache expiration time in seconds (5 minutes)
CACHE_EXPIRY = 300
last_cache_refresh = {
    'persons': datetime.now(),
    'reservations': datetime.now(),
    'transactions': datetime.now()
}

def load_persons_cache():
    """Load persons data into the in-memory cache"""
    global in_memory_cache_persons, last_cache_refresh
    person_ref = db.collection('persons')
    docs_person = person_ref.stream()
    in_memory_cache_persons.clear()
    for doc in docs_person:
        doc_dict = doc.to_dict()
        in_memory_cache_persons[doc.id] = doc_dict
    last_cache_refresh['persons'] = datetime.now()
    print(f"Persons cache refreshed. {len(in_memory_cache_persons)} records loaded.")

def load_reservations_cache():
    """Load hall reservations data into the in-memory cache"""
    global in_memory_cache_reservations, last_cache_refresh
    hall_reservations_ref = db.collection('hall_reservations')
    doc_hall_res = hall_reservations_ref.stream()
    in_memory_cache_reservations.clear()

    # Group reservations by id_number for quick lookup
    for doc in doc_hall_res:
        doc_dict = doc.to_dict()
        id_number = doc_dict.get('id_number')
        if id_number:
            if id_number not in in_memory_cache_reservations:
                in_memory_cache_reservations[id_number] = []
            doc_dict['doc_id'] = doc.id
            in_memory_cache_reservations[id_number].append(doc_dict)

    last_cache_refresh['reservations'] = datetime.now()
    print(f"Reservations cache refreshed. Records for {len(in_memory_cache_reservations)} users loaded.")

def load_transactions_cache():
    """Load transactions data into the in-memory cache"""
    global in_memory_cache_transactions, last_cache_refresh
    transactions_ref = db.collection('transactions')
    doc_transactions = transactions_ref.stream()
    in_memory_cache_transactions.clear()

    # Group transactions by id_number and transaction_type for quick lookup
    for doc in doc_transactions:
        doc_dict = doc.to_dict()
        id_number = doc_dict.get('id_number')
        transaction_type = doc_dict.get('transaction_type')

        if id_number and transaction_type:
            if id_number not in in_memory_cache_transactions:
                in_memory_cache_transactions[id_number] = {}

            in_memory_cache_transactions[id_number][transaction_type] = {
                'doc_id': doc.id,
                **doc_dict
            }

    last_cache_refresh['transactions'] = datetime.now()
    print(f"Transactions cache refreshed. Records for {len(in_memory_cache_transactions)} users loaded.")

def refresh_cache_if_needed(cache_type):
    """Check if cache needs refreshing based on expiry time"""
    time_diff = (datetime.now() - last_cache_refresh[cache_type]).total_seconds()
    if time_diff > CACHE_EXPIRY:
        if cache_type == 'persons':
            load_persons_cache()
        elif cache_type == 'reservations':
            load_reservations_cache()
        elif cache_type == 'transactions':
            load_transactions_cache()

# Initialize caches on module load
try:
    load_persons_cache()
    load_reservations_cache()
    load_transactions_cache()
except Exception as e:
    print(f"Error initializing cache: {str(e)}")
    # Continue without cache if there's an error

# if "402193114" in in_memory_cache:
#     print("exist")
## A disctionary for the transaction types in arabic and english
transaction_types = {
    "Route Marking": "طلب تعليم طريق",
    "Site Plan Guide": "طلب دليل مخطط موقع",
    "Building Permit": "طلب ترخيص مبنى",
    "Water Line NOC": "طلب عدم ممانعة لتقديم خدمة خط مياه",
    "Electricity Line NOC": "طلب عدم ممانعة لمد خط كهرباء",
    "Proof of Residence": "طلب إثبات سكن",
    "Employment Proof": "طلب إثبات عمل",
    "Request for Sale Transaction (for holders of Palestinian ID) – Plot Description and Clearance Certificate": "طلب معاملات صفقة بيع (لحملة الهوية الفلسطينية) وصف قطعة، براءة ذمة",
    "Request for Sale Transaction (for holders of Jerusalem ID) – Plot Description, Clearance Certificate, and Classification": "طلب معاملات صفقة بيع (لحملة هوية القدس) وصف قطعة، براءة ذمة وتصنيف",
    "Clearance for Kushan": "طلب معاملة براءة ذمة لاستخراج الكوشان",
    "other": "طلب معاملة أخرى او استفسار"
}
# About developers page view
def about_developers(request):
    return render(request, 'KLC_App/about_developers.html')

# News listing page view
def news_list(request):
    # Get all news from Firestore
    news_ref = db.collection('news_achievements').stream()
    news_list = []

    for doc in news_ref:
        data = doc.to_dict()
        # Add the document ID to the data
        data['id'] = doc.id

        # Format the date if present
        published_at = data.get('published_at', '')
        try:
            data['published_at'] = datetime.strptime(published_at, '%Y-%m-%d').strftime('%Y-%m-%d')
        except:
            data['published_at'] = ''

        # Process image URLs for display
        data = process_news_for_display(data)

        news_list.append(data)

    # Sort by date descending (newest first)
    news_list.sort(key=lambda x: x.get('published_at', ''), reverse=True)

    # Reorganize news for each page to ensure featured news is first
    # Group news items for pagination (9 items per page)
    grouped_news = []
    for i in range(0, len(news_list), 9):
        page_news = news_list[i:i+9]

        # Find featured news in this page
        featured_index = -1
        for idx, news in enumerate(page_news):
            if news.get('is_featured', False):
                featured_index = idx
                break

        # If there's a featured news in this page, move it to the first position
        if featured_index > 0:
            featured_news = page_news.pop(featured_index)
            page_news.insert(0, featured_news)

        # If no featured news in this page, mark the first news as featured
        # This ensures there's always a main news item in each page
        if featured_index == -1 and page_news:
            page_news[0]['is_featured'] = True

        grouped_news.append(page_news)

    context = {
        'news_list': news_list,
        'grouped_news': grouped_news,
        'total_news': len(news_list)
    }

    return render(request, 'KLC_App/news_list.html', context)

# Individual news article page view
def news_detail(request, news_id):
    # Get the news from Firestore
    news_ref = db.collection('news_achievements').document(news_id)
    news_doc = news_ref.get()

    if not news_doc.exists:
        # If news doesn't exist, redirect to index page
        return redirect('index')

    news_data = news_doc.to_dict()

    # Format the date if present
    published_at = news_data.get('published_at', '')
    try:
        news_data['published_at'] = datetime.strptime(published_at, '%Y-%m-%d').strftime('%Y-%m-%d')
    except:
        news_data['published_at'] = ''

    # Process image URLs for display
    news_data = process_news_for_display(news_data)

    # Get related news (same category)
    news_type = news_data.get('type', 'أخبار')
    related_news = []
    has_related_news = False

    # Get all news from the same category
    related_news_ref = db.collection('news_achievements').where('type', '==', news_type).stream()

    # Collect all related news items (excluding current one)
    for doc in related_news_ref:
        if doc.id != news_id:  # Exclude the current news
            data = doc.to_dict()
            data['id'] = doc.id

            # Format date
            doc_published_at = data.get('published_at', '')
            try:
                data['published_at'] = datetime.strptime(doc_published_at, '%Y-%m-%d').strftime('%Y-%m-%d')
            except:
                data['published_at'] = ''

            # Process image URLs for display
            data = process_news_for_display(data)

            related_news.append(data)

    # Randomly select up to 3 related news items if any exist
    import random
    if related_news:
        has_related_news = True
        # Shuffle the related news list to get random items
        random.shuffle(related_news)
        # Take up to 3 items
        related_news = related_news[:3]

    # Add the news ID to the context
    news_data['id'] = news_id

    context = {
        'news': news_data,
        'related_news': related_news,
        'has_related_news': has_related_news
    }
    # print(news_data)

    return render(request, 'KLC_App/news_detail.html', context)
# Helper function to get the best image URL from the new structure
def get_best_image_url(image_data):
    """
    Get the best available image URL from the image data

    Args:
        image_data: Either a URL string or a dictionary containing 'firebase_url' and 'local_url'

    Returns:
        The best available image URL (Firebase URL preferred, then local URL)
    """
    if not image_data:
        return None

    if isinstance(image_data, dict):
        # Prefer Firebase URL if available, otherwise use local URL
        return image_data.get('firebase_url') or image_data.get('local_url')

    # Legacy format - use as is
    return image_data

# Helper function to process news data for display
def process_news_for_display(news_data):
    """
    Process news data to ensure image URLs are ready for display

    Args:
        news_data: A dictionary containing news data

    Returns:
        The processed news data with image URLs ready for display
    """
    # Process main image
    image_src = news_data.get('image_src')
    news_data['image_src'] = get_best_image_url(image_src)

    # Process additional images
    additional_images = news_data.get('additional_images', [])
    processed_additional_images = []

    for img in additional_images:
        processed_img = get_best_image_url(img)
        if processed_img:
            processed_additional_images.append(processed_img)

    news_data['additional_images'] = processed_additional_images

    return news_data

# Index page - the main page of the website
def index(request):
    ## Noteeee: The following two functions will be used in the admin side
    # clean_person_data() # Run the function when the page loads to clean the persons file
    # import_excel_to_db()  # Run the function when the page loads

    # For the news and achievements section
    news_ref = db.collection('news_achievements').stream()
    news_achievements = []

    for doc in news_ref:
        data = doc.to_dict()
        # Add the document ID to the data
        data['id'] = doc.id

        published_at = data.get('published_at', '')
        # Parse date if present
        try:
            data['published_at'] = datetime.strptime(published_at, '%Y-%m-%d').strftime('%Y-%m-%d')
        except:
            data['published_at'] = ''

        # Process image URLs for display
        data = process_news_for_display(data)

        news_achievements.append(data)

    # Find featured news
    featured_news = None
    regular_news = []

    for news in news_achievements:
        if news.get('is_featured', False):
            featured_news = news
        else:
            regular_news.append(news)

    # Sort regular news by date descending
    regular_news.sort(key=lambda x: x.get('published_at', ''), reverse=True)

    # If no news is marked as featured, use the most recent one
    if not featured_news and regular_news:
        featured_news = regular_news.pop(0)

    # Get only the latest 4 news items for the index page
    latest_news = regular_news[:4]

    # Create a single group with the featured news and latest 4 news
    # Only include featured_news if it exists
    news_group = []
    if featured_news:
        news_group.append(featured_news)
    news_group.extend(latest_news)

    # Get total news count for the "View All" button
    total_news_count = len(regular_news) + (1 if featured_news else 0)

    # Get status and entered_id from session if they exist
    context = {
        'news_group': news_group,
        'featured_news': featured_news,
        'latest_news': latest_news,
        'total_news_count': total_news_count
    }

    # Add session data if it exists
    if 'status' in request.session:
        context['status'] = request.session.pop('status')
    if 'entered_id' in request.session:
        context['entered_id'] = request.session.pop('entered_id')

    return render(request, 'KLC_App/index.html', context)

# Welcome page - the first page of the website
def welcome(request):
    return render(request, 'KLC_App/welcome.html')
# Services page - the services page of the website
def services(request):
    user_debts_amount = request.session.get('klc_user_debts', 0)  # Get the debts amount from the session
    current_year = datetime.now().year  # Get current year for the modal
    return render(request, 'KLC_App/services.html', {
        "user_debts_amount": user_debts_amount,
        "current_year": current_year
    })

# To check if the person ID exists in the database and redirect accordingly
def check_person_id(request):
    if request.method == "POST":
        person_id = request.POST.get("person_id", "").strip()
        ## for protection if the required attribute is not found in the request
        if not person_id:  # If empty, reload the page without a status
            request.session['status'] = "error"
            request.session['entered_id'] = ""
            return redirect('index')
        ## Check if the person_id is numeric, Also for protection
        if not person_id.isnumeric():
            request.session['status'] = "not numeric"
            request.session['entered_id'] = person_id
            return redirect('index')

        # Check if national_id exists in Firestore
        person_doc = db.collection("persons").document(person_id).get()

        if person_doc.exists:
            request.session['klc_user_id'] = person_id # Save to user id in the session
            request.session['klc_user_debts'] = float(person_doc.to_dict().get('debts_amount_2024', 0)) # Save the debts amount in the session
            # Clear any unauthorized cache
            request.session.pop('last_unauth_user_id', None)
            # Clear any status from session since we're redirecting directly to services
            request.session.pop('status', None)
            request.session.pop('entered_id', None)
            return redirect('services')
        else:
            # Get last unauthorized ID from session
            last_unauth_user_id = request.session.get('last_unauth_user_id')
            ## Compare between the current id and the previous one
            if last_unauth_user_id == person_id:
                status = "unauthorized_repeat"
            else:
                status = "unauthorized"
                request.session['last_unauth_user_id'] = person_id  # Cash the unauth id attempt

            request.session['status'] = status
            request.session['entered_id'] = person_id
            return redirect('index')

    else:
        request.session['status'] = "error"
        request.session['entered_id'] = ""
        return redirect('index')


#### For hall reservation by the user
def reserve_hall(request):
    user_debts_amount = request.session.get('klc_user_debts', 0)  # Get the debts amount from the session
    if request.method == "POST":
        id_number = request.session.get('klc_user_id')  # Get the ID from the session
        full_name = request.POST.get("hallReservationFullName", "").strip()
        phone_number = request.POST.get("hallReservationPhoneNumber", "").strip()
        event_type = request.POST.get("hallReservationEventType", "").strip()
        start_date = request.POST.get("startDateInput", "").strip()
        end_date = request.POST.get("endDateInput", "").strip()

        if not id_number or not full_name or not phone_number or not event_type or not start_date or not end_date:
            return render(request, "KLC_App/services.html", {"status": "missing information", "user_debts_amount": user_debts_amount})
        ## Check if the user reserved the hall before or not
        if check_reservation_exist(id_number):
            return render(request, "KLC_App/services.html", {"status": "conflict slot", "user_debts_amount": user_debts_amount})

        ## Dates
        today = datetime.today().date()
        startDate = datetime.strptime(start_date, "%Y-%m-%d").date()
        endDate = datetime.strptime(end_date, "%Y-%m-%d").date()
        # To check if the dates are before today's date
        if startDate < today or endDate < today or startDate > endDate:
            return render(request, "KLC_App/services.html", {"status": "invalid dates", "user_debts_amount": user_debts_amount})
        # To check if the dates are already reserved or not
        if not check_date_slot(startDate, endDate):
            return render(request, "KLC_App/services.html", {"status": "this date is already reserved", "user_debts_amount": user_debts_amount})
        ### To get the local time zone
        jerusalem_tz = timezone('Asia/Jerusalem')
        reservation_data = {
            "id_number": id_number,
            "full_name": full_name,
            "phone_number": phone_number,
            "event_type": event_type,
            "start_date": start_date,
            "end_date": end_date,
            "reservation_status": "In Progress",  # by default when add reservation
            "admin_created": False,  # by default when add reservation
            "created_at": datetime.now(jerusalem_tz).strftime("%Y-%m-%d %H:%M:%S")
        }

        reservation_id = f"{id_number}_{reservation_data['created_at']}"
        db.collection("hall_reservations").document(reservation_id).set(reservation_data)

        # Update the cache with the new reservation
        if id_number not in in_memory_cache_reservations:
            in_memory_cache_reservations[id_number] = []

        reservation_data['doc_id'] = reservation_id
        in_memory_cache_reservations[id_number].append(reservation_data)

        return render(request, "KLC_App/services.html", {"status": "success", "user_debts_amount": user_debts_amount})

    return render(request, "KLC_App/services.html", {"status": "invalid request method", "user_debts_amount": user_debts_amount})

### The following view to make a transaction for the user
def make_transaction(request):
    user_debts_amount = request.session.get('klc_user_debts', 0)  # Get the debts amount from the session
    if request.method == "POST" and user_debts_amount <= 0: # Check if the user has no debts
        id_number = request.session.get('klc_user_id')  # Get the ID from the session
        full_name = request.POST.get("transactionsFullName", "").strip()
        phone_number = request.POST.get("transactionsPhoneNumber", "").strip()
        transaction_type = request.POST.get("transactionType", "").strip()
        additional_notes = request.POST.get("transactionAdditionalNotes", "").strip() # Optional for the user
        # check if the notes empty or not
        if additional_notes.strip() == "":
            additional_notes = "لا يوجد ملاحظات إضافية"

        if not id_number or not full_name or not phone_number or not transaction_type:
            return render(request, "KLC_App/services.html", {"status": "missing information", "user_debts_amount": user_debts_amount})
        ### To check if the user has already made a specific transaction or not
        if check_transaction_exist(id_number, transaction_types[transaction_type]):
            return render(request, "KLC_App/services.html", {"status": "conflict transaction", "user_debts_amount": user_debts_amount})
        ### To get the local time zone
        jerusalem_tz = timezone('Asia/Jerusalem')
        transaction_data = {
            "id_number": id_number,
            "full_name": full_name,
            "phone_number": phone_number,
            "transaction_type": transaction_types[transaction_type],  # Get the transaction type in Arabic
            "additional_notes": additional_notes,
            "transaction_status": "In Progress",  # Add default status
            "created_at": datetime.now(jerusalem_tz).strftime("%Y-%m-%d at %H:%M:%S")
        }

        transaction_id = f"{id_number}_{transaction_type}"
        db.collection("transactions").document(transaction_id).set(transaction_data)

        # Update the cache with the new transaction
        if id_number not in in_memory_cache_transactions:
            in_memory_cache_transactions[id_number] = {}
        in_memory_cache_transactions[id_number][transaction_types[transaction_type]] = {
            'doc_id': transaction_id,
            **transaction_data
        }

        return render(request, "KLC_App/services.html", {"status": "success", "user_debts_amount": user_debts_amount})

    return render(request, "KLC_App/services.html", {"status": "invalid request method", "user_debts_amount": user_debts_amount})
###

# The following view to check if the user has already made a specific transaction or not
def check_transaction_exist(id_number, transaction_type):
    # First check if we need to refresh the cache
    refresh_cache_if_needed('transactions')

    # Check in the in-memory cache first
    if id_number in in_memory_cache_transactions:
        if transaction_type in in_memory_cache_transactions[id_number]:
            return True  # Found in cache

    # If not found in cache, check the database as a fallback
    # This also handles the case where the cache might not be fully up-to-date
    collection_ref = db.collection('transactions')
    query = collection_ref.where('id_number', '==', id_number).where('transaction_type', '==', transaction_type).stream()

    # Check if any document matches
    for doc in query:
        # Update the cache with this transaction
        if id_number not in in_memory_cache_transactions:
            in_memory_cache_transactions[id_number] = {}

        in_memory_cache_transactions[id_number][transaction_type] = {
            'doc_id': doc.id,
            **doc.to_dict()
        }

        return True  # Found a match in the database

    return False  # No match found in cache or database

# The following view to make suggestion or complaint for the user
def make_suggestions_complaints(request):
    user_debts_amount = request.session.get('klc_user_debts', 0)  # Get the debts amount from the session
    if request.method == "POST":
        id_number = request.session.get('klc_user_id')  # Get the ID from the session
        full_name = request.POST.get("suggestionsProviderName", "").strip()
        message = request.POST.get("suggestionsMessage", "").strip()
        if full_name == "":
            full_name = "بدون إسم"
        if not id_number or not message:
            return render(request, "KLC_App/services.html", {"suggestions_status": "missing suggestions complaints information", "user_debts_amount": user_debts_amount})
        # Check if the user has already made 2 suggestions/complaints (the max number) or not
        if not check_suggestion_count(id_number):
            return render(request, "KLC_App/services.html", {"suggestions_status": "conflict suggestions complaints", "user_debts_amount": user_debts_amount})
        # Generate a unique ID for the suggestion/complaint
        jerusalem_tz = timezone('Asia/Jerusalem')
        suggestions_complaints_data = {
            "id_number": id_number,
            "full_name": full_name,
            "message": message,
            "created_at": datetime.now(jerusalem_tz).strftime("%Y-%m-%d at %H:%M:%S")
        }
        unique_id = f"{id_number}_{suggestions_complaints_data['created_at']}"
        db.collection("suggestions_complaints").document(unique_id).set(suggestions_complaints_data)
        return render(request, "KLC_App/services.html", {"suggestions_status": "success", "user_debts_amount": user_debts_amount})

    return render(request, "KLC_App/services.html", {"suggestions_status": "invalid suggestions complaints request method", "user_debts_amount": user_debts_amount})

def check_suggestion_count(id_number):
    collection_ref = db.collection('suggestions_complaints')
    # Query documents where id_number field equals the given id_number
    query = collection_ref.where('id_number', '==', id_number).stream()
    # Check if the number of user suggestions is 2 or less
    count = sum(1 for _ in query)
    if count == 2:
        return False # Found a match, meaning the user has already made 2 suggestions/complaints

    return True # Maybe match found, meaning the user has not made 2 suggestions/complaints and still able to make another one
# Admin dashboard views
def admin_make_transaction(request):
    if request.method == "POST":
        id_number = request.POST.get('transactionsIdNumber')
        full_name = request.POST.get("transactionsFullName", "").strip()
        phone_number = request.POST.get("transactionsPhoneNumber", "").strip()
        transaction_type = request.POST.get("transactionType", "").strip()
        additional_notes = request.POST.get("transactionAdditionalNotes", "").strip()

        if additional_notes.strip() == "":
            additional_notes = "لا يوجد ملاحظات إضافية"

        if not id_number or not full_name or not phone_number or not transaction_type:
            return JsonResponse({"status": "الرجاء تعبئة كل الحقول المطلوبة."})

        if check_transaction_exist(id_number, transaction_types[transaction_type]):
            return JsonResponse({"status": "هذا الشخص لديه طلب من هذا النوع بالفعل."})

        jerusalem_tz = timezone('Asia/Jerusalem')
        transaction_data = {
            "id_number": id_number,
            "full_name": full_name,
            "phone_number": phone_number,
            "transaction_type": transaction_types[transaction_type],  # Get the transaction type in Arabic
            "additional_notes": additional_notes,
            "transaction_status": "In Progress",  # Add default status
            "created_at": datetime.now(jerusalem_tz).strftime("%Y-%m-%d at %H:%M:%S")
        }

        transaction_id = f"{id_number}_{transaction_type}"
        db.collection("transactions").document(transaction_id).set(transaction_data)

        # Update the cache with the new transaction
        if id_number not in in_memory_cache_transactions:
            in_memory_cache_transactions[id_number] = {}
        in_memory_cache_transactions[id_number][transaction_types[transaction_type]] = {
            'doc_id': transaction_id,
            **transaction_data
        }

        return JsonResponse({"status": "success"})

    return JsonResponse({"status": "invalid request method"})

# Add new function to handle status updates
def update_transaction_status(request, transaction_id):
    if request.method == 'POST':
        try:
            # Get the receipt number from the form
            receipt_number = request.POST.get('receipt_number', '')

            # Get the transaction data before updating to update the cache
            transaction_doc = db.collection('transactions').document(transaction_id).get()
            if transaction_doc.exists:
                transaction_data = transaction_doc.to_dict()
                id_number = transaction_data.get('id_number')
                transaction_type = transaction_data.get('transaction_type')

                # Update in database
                jerusalem_tz = timezone('Asia/Jerusalem')
                completed_at = datetime.now(jerusalem_tz).strftime("%Y-%m-%d at %H:%M:%S")
                transaction_ref = db.collection('transactions').document(transaction_id)

                update_data = {
                    'transaction_status': 'Done',
                    'completed_at': completed_at
                }

                # Add receipt number if provided
                if receipt_number:
                    update_data['receipt_number'] = receipt_number

                transaction_ref.update(update_data)

                # Update cache if the id_number and transaction_type exist
                if id_number and transaction_type and id_number in in_memory_cache_transactions:
                    if transaction_type in in_memory_cache_transactions[id_number]:
                        # Update the transaction status in the cache
                        in_memory_cache_transactions[id_number][transaction_type]['transaction_status'] = 'Done'
                        in_memory_cache_transactions[id_number][transaction_type]['completed_at'] = completed_at
                        if receipt_number:
                            in_memory_cache_transactions[id_number][transaction_type]['receipt_number'] = receipt_number

                # Return JSON response if it's an AJAX request
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'status': 'success',
                        'message': 'تم تحديث حالة الطلب بنجاح ✅'
                    })

                messages.success(request, 'تم تحديث حالة الطلب بنجاح ✅')
            else:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({
                        'status': 'error',
                        'message': 'لم يتم العثور على الطلب'
                    })
                messages.error(request, 'لم يتم العثور على الطلب')
        except Exception as e:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'error',
                    'message': f'حدث خطأ أثناء تحديث حالة الطلب: {str(e)}'
                })
            messages.error(request, f'حدث خطأ أثناء تحديث حالة الطلب: {str(e)}')

    return redirect('admin_transactions')

# def delete_suggestion_admin(request, s_id):
#     if request.method == 'POST':
#         try:
#             db.collection('suggestions_complaints').document(s_id).delete()
#             messages.success(request, "تم حذف الطلب بنجاح")
#         except Exception as e:
#             messages.error(request, f"خطأ في الحذف: {str(e)}")
#     return redirect('admin_dashboard')

def parse_date(date_str):
    for fmt in ("%Y-%m-%d", "%d-%m-%Y", "%m/%d/%Y"):
        try:
            return datetime.strptime(date_str, fmt).date()
        except ValueError:
            continue
    return None

def check_password(plain_password, hashed_password_from_db):
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password_from_db.encode('utf-8'))

def admin_login(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        doc_ref = db.collection('admin').document(username)
        doc = doc_ref.get()

        if doc.exists:
            data = doc.to_dict()
            hashed_password = data.get('password')
            if hashed_password and check_password(password, hashed_password):
                request.session['is_admin_authenticated'] = True
                # Store the admin username in the session for permission checks
                request.session['admin_username'] = username
                return redirect('/admin')

        return render(request, 'KLC_App/admin_login.html', {'error': 'Username or password incorrect'})

    return render(request, 'KLC_App/admin_login.html')

def admin_reserve_hall(request):
    if request.method == "POST":
        # print("Raw POST:", request.POST.dict())
        id_number = request.POST.get("idNumber", "").strip()
        phone_numberr = request.POST.get("phoneNumber", "").strip()
        event_type = request.POST.get("eventType", "").strip()
        start_date = request.POST.get("startDate", "").strip()
        end_date = request.POST.get("endDate", "").strip()
        fullName = request.POST.get("fullName", "").strip()

        # Check if reservation exists for this user
        if check_reservation_exist(id_number):
            return JsonResponse({"status": "هذا الشخص لديه حجز بالفعل."})

        # print("Received start_date:", start_date)
        # print("Received end_date:", end_date)
        # Parse and validate dates
        today = datetime.today().date()
        startDate = parse_date(start_date)
        endDate = parse_date(end_date)

        # print("Parsed startDate:", startDate)
        # print("Parsed endDate:", endDate)

        if not startDate or not endDate:
            return JsonResponse({"status": "الرجاء تعبئة كل الحقول المطلوبة."})

        if startDate < today or endDate < today or startDate > endDate or not check_date_slot(startDate, endDate):
            return JsonResponse({"status": "هذا التاريخ محجوز بالفعل."})

        # Get user data
        # user_data = person_doc.to_dict()
        jerusalem_tz = timezone('Asia/Jerusalem')

        reservation_data = {
            "id_number": id_number,
            "full_name": fullName,
            "phone_number": phone_numberr,
            "event_type": event_type,
            "start_date": startDate.strftime("%Y-%m-%d"),
            "end_date": endDate.strftime("%Y-%m-%d"),
            "created_at": datetime.now(jerusalem_tz).strftime("%Y-%m-%d %H:%M:%S"),
            "admin_created": True,
            "reservation_status": "In Progress"   # by default when add reservation
        }

        reservation_id = f"{id_number}_{reservation_data['created_at']}"
        db.collection("hall_reservations").document(reservation_id).set(reservation_data)

        # Update the cache with the new reservation
        if id_number not in in_memory_cache_reservations:
            in_memory_cache_reservations[id_number] = []

        reservation_data['doc_id'] = reservation_id
        in_memory_cache_reservations[id_number].append(reservation_data)

        return JsonResponse({"status": "success"})

    return JsonResponse({"status": "invalid request method"})

def check_reservation_exist(id_number):
    # First check if we need to refresh the cache
    refresh_cache_if_needed('reservations')

    # Check in the in-memory cache first
    if id_number in in_memory_cache_reservations and in_memory_cache_reservations[id_number]:
        return True  # Found in cache

    # If not found in cache, check the database as a fallback
    collection_ref = db.collection('hall_reservations')
    query = collection_ref.where('id_number', '==', id_number).stream()

    # Check if any document matches
    reservations_found = []
    for doc in query:
        doc_dict = doc.to_dict()
        doc_dict['doc_id'] = doc.id
        reservations_found.append(doc_dict)

    if reservations_found:
        # Update the cache with these reservations
        in_memory_cache_reservations[id_number] = reservations_found
        return True  # Found a match in the database

    return False  # No match found in cache or database

def confirm_reservation_admin(request, reservation_id):
    if request.method == "POST":
        try:
            reservation_ref = db.collection('hall_reservations').document(reservation_id)
            reservation_ref.update({
                'reservation_status': 'confirmed'
            })
            messages.success(request, 'تم تأكيد الحجز بنجاح ✅')
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء تأكيد الحجز: {str(e)}')

    return redirect('admin_reservations')


# @login_required(login_url='user_login')
def admin_dashboard(request):
    if request.method == 'POST':
        form = UploadFileForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['file']
            try:
                import_excel_to_db(excel_file)
                messages.success(request, "تم تحديث البيانات بنجاح")
            except Exception as e:
                messages.error(request, f"خطأ في رفع الملف: {str(e)}")
            return redirect('admin_dashboard')

    # الاتصال بقاعدة البيانات
    db = settings.DB
    users_ref = db.collection('persons')

    # معالجة البحث
    users_count = 0
    debt_sum = 0
    hall_reservations_count = 0

    try:
        # جلب جميع المستخدمين إلى قائمة
        all_users = list(users_ref.stream())

        for user in all_users:
            user_data = user.to_dict()

            # تحويل الحقول إلى قيم افتراضية إذا لم تكن موجودة
            debt = int(user_data.get('debts_amount_2024', 0))

            # حساب العدد وإجمالي الديون
            users_count += 1
            debt_sum += debt

    except Exception as e:
        messages.error(request, f"خطأ في جلب البيانات: {str(e)}")

    # Add hall reservations data
    reservations_ref = db.collection("hall_reservations").stream()
    hall_reservations = []
    for res in reservations_ref:
        res_data = res.to_dict()
        res_data['id'] = res.id
        hall_reservations.append(res_data)

    hall_reservations_count = len(hall_reservations)

    # Sort reservations by created_at date (newest first)
    def get_reservation_date(reservation):
        created_at = reservation.get('created_at', '')
        if created_at:
            try:
                return datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    return datetime.strptime(created_at, '%Y-%m-%d')
                except ValueError:
                    pass
        return datetime.min

    hall_reservations.sort(key=get_reservation_date, reverse=True)

    # Get transactions data
    transactions_ref = db.collection("transactions").stream()
    transactions = []
    for trans in transactions_ref:
        trans_data = trans.to_dict()
        trans_data['id'] = trans.id
        transactions.append(trans_data)
    transactions_count = len(transactions)

    # Sort transactions by created_at date (newest first)
    def get_transaction_date(transaction):
        created_at = transaction.get('created_at', '')
        if created_at:
            try:
                return datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    return datetime.strptime(created_at, '%Y-%m-%d at %H:%M:%S')
                except ValueError:
                    pass
        return datetime.min

    transactions.sort(key=get_transaction_date, reverse=True)

    # Get suggestions data
    suggestions_ref = db.collection('suggestions_complaints').stream()
    suggestions = []
    for suggestion in suggestions_ref:
        suggestion_data = suggestion.to_dict()
        suggestion_data['id'] = suggestion.id
        suggestions.append(suggestion_data)

    # Sort suggestions by created_at date (newest first)
    def get_suggestion_date(suggestion):
        created_at = suggestion.get('created_at', '')
        if created_at:
            try:
                return datetime.strptime(created_at, '%Y-%m-%d at %H:%M:%S')
            except ValueError:
                try:
                    return datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    pass
        return datetime.min

    suggestions.sort(key=get_suggestion_date, reverse=True)

    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    context = {
        'users_count': users_count,
        'total_debt': f"{debt_sum:,} شيقل",
        'hall_reservations': hall_reservations[:5],  # Only get the 5 most recent
        'hall_reservations_count': hall_reservations_count,
        'transactions': transactions[:5],  # Only get the 5 most recent
        'transactions_count': transactions_count,
        'suggestions': suggestions[:2],  # Only get the 2 most recent
        'active_page': 'dashboard',
        'admin_username': admin_username,  # Add the admin username to the context
    }

    return render(request, 'KLC_App/admin/admin_dashboard.html', context)

def admin_users(request):
    # الاتصال بقاعدة البيانات
    db = settings.DB
    users_ref = db.collection('persons')

    # معالجة البحث
    search_query = request.GET.get('q', '').strip().lower()
    users = []

    try:
        # جلب جميع المستخدمين إلى قائمة
        all_users = list(users_ref.stream())

        for user in all_users:
            user_data = user.to_dict()

            # تحويل الحقول إلى قيم افتراضية إذا لم تكن موجودة
            national_id = str(user_data.get('national_id', '')).lower()
            name = user_data.get('name', 'بدون اسم').lower()
            debt = int(user_data.get('debts_amount_2024', 0))

            # إضافة البيانات المنسقة
            if not search_query or search_query in national_id or search_query in name:
                users.append({
                    'id': user.id,
                    'national_id': national_id,
                    'name': user_data.get('name', 'بدون اسم'),
                    'debt': f"{debt:,} شيقل",
                })

    except Exception as e:
        messages.error(request, f"خطأ في جلب البيانات: {str(e)}")

    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    # Create bulk debt update form
    bulk_debt_form = BulkDebtUpdateForm()

    context = {
        'users': users,
        'search_query': search_query,
        'active_page': 'users',
        'admin_username': admin_username,
        'bulk_debt_form': bulk_debt_form,
    }

    return render(request, 'KLC_App/admin/admin_users.html', context)

def bulk_debt_update(request):
    """View to handle bulk debt updates for all users"""
    if request.method == 'POST':
        form = BulkDebtUpdateForm(request.POST)
        if form.is_valid():
            debt_amount = float(form.cleaned_data['debt_amount'])

            try:
                db = settings.DB
                users_ref = db.collection('persons')

                # Get all users
                all_users = list(users_ref.stream())
                updated_count = 0

                # Update each user's debt
                for user_doc in all_users:
                    user_data = user_doc.to_dict()
                    # Get current debt as string, convert to float for calculation
                    current_debt_str = user_data.get('debts_amount_2024', '0')
                    try:
                        current_debt = float(current_debt_str) if current_debt_str else 0
                    except (ValueError, TypeError):
                        current_debt = 0

                    new_debt = current_debt + debt_amount

                    # Convert back to string for storage (allow negative values)
                    new_debt_str = str(int(new_debt)) if new_debt.is_integer() else str(new_debt)

                    # Update the user's debt as string
                    user_doc.reference.update({
                        'debts_amount_2024': new_debt_str
                    })
                    updated_count += 1

                # Create success message based on operation type
                if debt_amount > 0:
                    action_text = f"تمت إضافة {debt_amount:,} شيقل"
                else:
                    action_text = f"تم خصم {abs(debt_amount):,} شيقل"

                success_message = f"تم تحديث ديون {updated_count} مستخدم بنجاح. {action_text} لكل مستخدم."

                messages.success(request, success_message)

            except Exception as e:
                messages.error(request, f"حدث خطأ أثناء تحديث الديون: {str(e)}")
        else:
            messages.error(request, "يرجى إدخال مبلغ صحيح")

    return redirect('admin_users')

def add_reservation(request):
    """View for adding a new reservation as a full page (not modal)"""
    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    context = {
        'active_page': 'reservations',
        'admin_username': admin_username,
    }
    return render(request, 'KLC_App/admin/add_reservation.html', context)

def add_transaction(request):
    """View for adding a new transaction as a full page (not modal)"""
    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    context = {
        'active_page': 'transactions',
        'admin_username': admin_username,
    }
    return render(request, 'KLC_App/admin/add_transaction.html', context)

def admin_reservations(request):
    # الاتصال بقاعدة البيانات
    db = settings.DB

    # Get current date for comparison
    current_date = datetime.now()

    # Add hall reservations data
    reservations_ref = db.collection("hall_reservations").stream()
    hall_reservations = []
    reservations_to_delete = []

    for res in reservations_ref:
        res_data = res.to_dict()
        res_data['id'] = res.id

        # Parse the creation date
        created_at = res_data.get('created_at', '')
        creation_date = None

        if created_at:
            try:
                creation_date = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    creation_date = datetime.strptime(created_at, '%Y-%m-%d')
                except ValueError:
                    pass

        # If we have a valid creation date and the reservation is not confirmed
        if creation_date and res_data.get('reservation_status') != 'confirmed':
            # Calculate days difference
            days_difference = (current_date - creation_date).days

            # Add days_remaining to the reservation data
            res_data['days_remaining'] = max(0, 3 - days_difference)

            # If it's been more than 3 days and not confirmed, mark for deletion
            if days_difference > 3:
                reservations_to_delete.append(res_data['id'])
            else:
                hall_reservations.append(res_data)
        else:
            # For confirmed reservations or those without valid creation date
            if creation_date:
                res_data['days_remaining'] = 0  # No days remaining for confirmed reservations
            hall_reservations.append(res_data)

    # Delete reservations that are older than 3 days and not confirmed
    for reservation_id in reservations_to_delete:
        try:
            # Get the reservation data before deleting to update the cache
            reservation_doc = db.collection('hall_reservations').document(reservation_id).get()
            if reservation_doc.exists:
                reservation_data = reservation_doc.to_dict()
                id_number = reservation_data.get('id_number')

                # Delete from database
                db.collection('hall_reservations').document(reservation_id).delete()

                # Update cache if the id_number exists
                if id_number and id_number in in_memory_cache_reservations:
                    # Remove the reservation with this doc_id from the cache
                    in_memory_cache_reservations[id_number] = [
                        r for r in in_memory_cache_reservations[id_number]
                        if r.get('doc_id') != reservation_id
                    ]

                    # If no more reservations for this user, remove the entry
                    if not in_memory_cache_reservations[id_number]:
                        del in_memory_cache_reservations[id_number]
        except Exception as e:
            print(f"Error deleting expired reservation {reservation_id}: {str(e)}")

    # Sort reservations by created_at date (newest first)
    def get_reservation_date(reservation):
        created_at = reservation.get('created_at', '')
        if created_at:
            try:
                return datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    return datetime.strptime(created_at, '%Y-%m-%d')
                except ValueError:
                    pass
        return datetime.min

    hall_reservations.sort(key=get_reservation_date, reverse=True)

    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    context = {
        'hall_reservations': hall_reservations,
        'active_page': 'reservations',
        'admin_username': admin_username,
    }

    return render(request, 'KLC_App/admin/admin_reservations.html', context)

def admin_transactions(request):
    # الاتصال بقاعدة البيانات
    db = settings.DB

    # Get transactions data
    transactions_ref = db.collection("transactions").stream()
    transactions = []
    completed_count = 0

    for trans in transactions_ref:
        trans_data = trans.to_dict()
        trans_data['id'] = trans.id

        # Only include transactions that are not completed (In Progress)
        # or completed but don't have a receipt number
        if trans_data.get('transaction_status') != 'Done' or not trans_data.get('receipt_number'):
            transactions.append(trans_data)
        else:
            completed_count += 1

    # Sort transactions by created_at date (newest first)
    def get_transaction_date(transaction):
        created_at = transaction.get('created_at', '')
        if created_at:
            try:
                return datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    return datetime.strptime(created_at, '%Y-%m-%d at %H:%M:%S')
                except ValueError:
                    pass
        return datetime.min

    transactions.sort(key=get_transaction_date, reverse=True)

    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    context = {
        'transactions': transactions,
        'completed_count': completed_count,
        'active_page': 'transactions',
        'admin_username': admin_username,
    }

    return render(request, 'KLC_App/admin/admin_transactions.html', context)

def admin_suggestions(request):
    # الاتصال بقاعدة البيانات
    db = settings.DB

    suggestions_ref = db.collection('suggestions_complaints')
    docs = list(suggestions_ref.stream())

    suggestions = []
    for doc in docs:
        data = doc.to_dict()
        suggestions.append({
            'id': doc.id,
            'full_name': data.get('full_name', ''),
            'id_number': data.get('id_number', ''),
            'message': data.get('message', ''),
            'created_at': data.get('created_at', '')
        })

    # Get the admin username from the session
    admin_username = request.session.get('admin_username', '')

    context = {
        'suggestions': suggestions,
        'active_page': 'suggestions',
        'admin_username': admin_username,
    }

    return render(request, 'KLC_App/admin/admin_suggestions.html', context)


def delete_reservation_admin(request, reservation_id):
    if request.method == 'POST':
        try:
            # Get the reservation data before deleting to update the cache
            reservation_doc = db.collection('hall_reservations').document(reservation_id).get()
            if reservation_doc.exists:
                reservation_data = reservation_doc.to_dict()
                id_number = reservation_data.get('id_number')

                # Delete from database
                db.collection('hall_reservations').document(reservation_id).delete()

                # Update cache if the id_number exists
                if id_number and id_number in in_memory_cache_reservations:
                    # Remove the reservation with this doc_id from the cache
                    in_memory_cache_reservations[id_number] = [
                        r for r in in_memory_cache_reservations[id_number]
                        if r.get('doc_id') != reservation_id
                    ]

                    # If no more reservations for this user, remove the entry
                    if not in_memory_cache_reservations[id_number]:
                        del in_memory_cache_reservations[id_number]

            messages.success(request, "تم حذف الحجز بنجاح")
        except Exception as e:
            messages.error(request, f"خطأ في الحذف: {str(e)}")
    return redirect('admin_reservations')
# views.py

def delete_suggestion_admin(request, suggestion_id):
    if request.method == 'POST':
        try:
            db.collection('suggestions_complaints').document(suggestion_id).delete()
            messages.success(request, "تم الحذف بنجاح")
        except Exception as e:
            messages.error(request, f"خطأ في الحذف: {str(e)}")
    return redirect('admin_suggestions')

def delete_transaction_admin(request, transaction_id):
    if request.method == 'POST':
        try:
            # Get the transaction data before deleting to update the cache
            transaction_doc = db.collection('transactions').document(transaction_id).get()
            if transaction_doc.exists:
                transaction_data = transaction_doc.to_dict()
                id_number = transaction_data.get('id_number')
                transaction_type = transaction_data.get('transaction_type')
                transaction_status = transaction_data.get('transaction_status')
                receipt_number = transaction_data.get('receipt_number')

                # Check if the transaction is completed and has a receipt number
                if transaction_status == 'Done' and receipt_number:
                    messages.error(request, "لا يمكن حذف الطلب المكتمل الذي له رقم إيصال. هذه المعاملات محفوظة في سجل الإحصائيات.")
                    return redirect('admin_transactions')

                # Delete from database
                db.collection('transactions').document(transaction_id).delete()

                # Update cache if the id_number and transaction_type exist
                if id_number and transaction_type and id_number in in_memory_cache_transactions:
                    if transaction_type in in_memory_cache_transactions[id_number]:
                        # Remove this transaction type from the cache
                        del in_memory_cache_transactions[id_number][transaction_type]

                    # If no more transactions for this user, remove the entry
                    if not in_memory_cache_transactions[id_number]:
                        del in_memory_cache_transactions[id_number]

                messages.success(request, "تم حذف الطلب بنجاح")
            else:
                messages.error(request, "لم يتم العثور على الطلب")
        except Exception as e:
            messages.error(request, f"خطأ في الحذف: {str(e)}")
    return redirect('admin_transactions')

def verify_manager_password(password):
    """
    Verify the manager password against the stored hashed password in the database.
    """
    try:
        # Get the manager password document
        manager_password_doc = db.collection('system_settings').document('manager_password').get()

        if not manager_password_doc.exists:
            # If no manager password is set, use the default password
            return password == "klc@admin"

        # Get the hashed password from the document
        manager_data = manager_password_doc.to_dict()
        hashed_password = manager_data.get('password')

        # Verify the password
        if hashed_password:
            return check_password(password, hashed_password)

        return False
    except Exception:
        # In case of any error, fall back to the default password
        return password == "klc@admin"

def delete_completed_transaction(request, transaction_id):
    """
    Special endpoint to delete completed transactions with receipt numbers.
    This requires admin password verification and specific admin permissions.
    """
    if request.method == "POST":
        try:
            # Check if the current admin is authorized to delete confirmed transactions (only 'ahmad' has permission)
            current_admin = request.session.get('admin_username')
            if current_admin != 'ahmad':
                return JsonResponse({
                    "status": "error",
                    "message": "ليس لديك صلاحية لحذف المعاملات المؤكدة"
                })

            # Verify the admin password
            password = request.POST.get('manager_password')
            if not password or not verify_manager_password(password):
                return JsonResponse({
                    "status": "error",
                    "message": "كلمة المرور غير صحيحة"
                })

            # الاتصال بقاعدة البيانات
            db = settings.DB

            # حذف الطلب
            transaction_ref = db.collection("transactions").document(transaction_id)
            transaction = transaction_ref.get()

            if transaction.exists:
                transaction_data = transaction.to_dict()
                id_number = transaction_data.get('id_number')
                transaction_type = transaction_data.get('transaction_type')

                # Delete from database
                transaction_ref.delete()

                # Update cache if the id_number and transaction_type exist
                if id_number and transaction_type and id_number in in_memory_cache_transactions:
                    if transaction_type in in_memory_cache_transactions[id_number]:
                        # Remove this transaction type from the cache
                        del in_memory_cache_transactions[id_number][transaction_type]

                    # If no more transactions for this user, remove the entry
                    if not in_memory_cache_transactions[id_number]:
                        del in_memory_cache_transactions[id_number]

                return JsonResponse({
                    "status": "success",
                    "message": "تم حذف الطلب بنجاح"
                })
            else:
                return JsonResponse({
                    "status": "error",
                    "message": "لم يتم العثور على الطلب"
                })
        except Exception as e:
            return JsonResponse({
                "status": "error",
                "message": f"خطأ في الحذف: {str(e)}"
            })

    return JsonResponse({
        "status": "error",
        "message": "طريقة طلب غير صالحة"
    })

def add_user(request):
    # Authorization check (uncomment if needed)
    # if not request.user.is_authenticated or not request.user.is_staff:
    #     return redirect('login')

    if request.method == 'POST':
        form = AddUserForm(request.POST)
        if form.is_valid():
            db = settings.DB
            users_ref = db.collection('persons')

            try:
                national_id = form.cleaned_data['national_id']

                # Check if document with this ID already exists
                doc_ref = users_ref.document(national_id)
                if doc_ref.get().exists:
                    messages.error(request, "رقم الهوية مسجل مسبقاً")
                    return render(request, 'KLC_App/admin/add_user.html', {'form': form})

                # Create new document with national_id as the document ID
                new_user = {
                    'national_id': national_id,
                    'name': form.cleaned_data['name'],
                    'debts_amount_2024': float(form.cleaned_data['debts_amount_2024']),
                    # 'created_at': firestore.SERVER_TIMESTAMP  # Uncomment if needed
                }

                # Add document with specific ID
                doc_ref.set(new_user)

                messages.success(request, "تمت إضافة المستخدم بنجاح")
                return redirect('admin_users')

            except Exception as e:
                messages.error(request, f"خطأ في الإضافة: {str(e)}")
    else:
        form = AddUserForm()

    return render(request, 'KLC_App/admin/add_user.html', {'form': form})

def delete_user(request, user_id):
    if request.method == 'POST':
        try:
            db = settings.DB
            db.collection('persons').document(user_id).delete()
            messages.success(request, "تم حذف المستخدم بنجاح")
        except Exception as e:
            messages.error(request, f"خطأ في الحذف: {str(e)}")
    return redirect('admin_users')

def settle_debt(request, user_id):
    db = settings.DB
    user_ref = db.collection('persons').document(user_id)

    try:
        user_doc = user_ref.get()
        if not user_doc.exists:
            messages.error(request, "المستخدم غير موجود")
            return redirect('admin_users')

        user_data = user_doc.to_dict()

        if request.method == 'POST':
            try:
                amount = float(request.POST.get('amount', 0))
                if amount <= 0:
                    raise ValueError

                current_debt = user_data.get('debts_amount_2024', 0)
                new_debt =  current_debt - amount            # اذا المبلغ بالسالب معناها المواطن باقيلو مبلغ

                user_ref.update({'debts_amount_2024': new_debt})
                messages.success(request, f"تم تسديد {amount} شيقل بنجاح. المتبقي: {new_debt} شيقل")

            except ValueError:
                messages.error(request, "المبلغ المدخل غير صحيح")
            except Exception as e:
                messages.error(request, f"خطأ في التسديد: {str(e)}")

            return redirect('admin_users')

        # For GET requests
        return render(request, 'KLC_App/admin/settle_debt_modal.html', {
            'current_debt': user_data.get('debts_amount_2024', 0),
            'user_id': user_id,
            'user_name': user_data.get('name', 'غير معروف'),
            'user_id_number': user_id
        })

    except Exception as e:
        messages.error(request, f"حدث خطأ: {str(e)}")
        return redirect('admin_users')

def edit_user(request, user_id):
    # Authorization check (uncomment if needed)
    # if not request.user.is_authenticated or not request.user.is_staff:
    #     return redirect('login')

    db = settings.DB
    users_ref = db.collection('persons')
    user_ref = users_ref.document(user_id)

    try:
        user_doc = user_ref.get()
        if not user_doc.exists:
            messages.error(request, "المستخدم غير موجود")
            return redirect('admin_users')

        user_data = user_doc.to_dict()

        if request.method == 'POST':
            form = EditUserForm(request.POST)
            if form.is_valid():
                new_national_id = form.cleaned_data['id_number']
                new_name = form.cleaned_data['full_name']
                new_debt = float(form.cleaned_data['owed_amount'])

                # Check if national ID is being changed
                if new_national_id != user_id:
                    # Check if new ID already exists
                    new_user_ref = users_ref.document(new_national_id)
                    if new_user_ref.get().exists:
                        messages.error(request, "رقم الهوية الجديد مسجل مسبقاً")
                        return render(request, 'KLC_App/admin/edit_user.html', {
                            'form': form,
                            'user_id': user_id
                        })

                    # Create new document with updated ID
                    new_user_ref.set({
                        'national_id': new_national_id,
                        'name': new_name,
                        'debts_amount_2024': new_debt
                    })

                    # Delete old document
                    user_ref.delete()
                else:
                    # Update existing document
                    user_ref.update({
                        'name': new_name,
                        'debts_amount_2024': new_debt
                    })

                messages.success(request, "تم تحديث بيانات المستخدم بنجاح")
                return redirect('admin_users')

        else:
            # Initialize form with existing data using correct field names
            form = EditUserForm(initial={
                'id_number': user_id,  # Document ID is the original national ID
                'full_name': user_data.get('name'),
                'owed_amount': user_data.get('debts_amount_2024', 0),
            })

        return render(request, 'KLC_App/admin/edit_user.html', {
            'form': form,
            'user_id': user_id
        })

    except Exception as e:
        messages.error(request, f"حدث خطأ: {str(e)}")
        return redirect('admin_users')

def admin_logout(request):
    # Clear all admin-related session data
    request.session.pop('is_admin_authenticated', None)
    request.session.pop('admin_username', None)
    return redirect('admin_login')

def admin_management(request):
    """
    View for managing admin accounts and passwords.
    This section is protected with a special password and only accessible by specific admin.
    """
    # Check if the admin is authenticated
    if not request.session.get('is_admin_authenticated'):
        return redirect('admin_login')

    # Check if the current admin is authorized to access this page (only 'ahmad' has access)
    current_admin = request.session.get('admin_username')
    if current_admin != 'ahmad':
        # Return the template with an access_denied flag
        return render(request, 'KLC_App/admin/admin_management.html', {
            'active_page': 'admin_management',
            'access_denied': True
        })

    # Get or initialize the manager password
    manager_password_doc = db.collection('system_settings').document('manager_password').get()
    has_manager_password = False
    if manager_password_doc.exists:
        has_manager_password = True

    # Check if the admin has access to this section
    if request.method == 'POST':
        action = request.POST.get('action')

        # Verify the admin password for this section
        admin_password = request.POST.get('admin_password')
        if admin_password != "klc@admin":
            messages.error(request, "كلمة المرور غير صحيحة")
            return render(request, 'KLC_App/admin/admin_management.html', {
                'active_page': 'admin_management',
                'has_manager_password': has_manager_password
            })

        # Handle different actions
        if action == 'change_admin_password':
            username = request.POST.get('username')
            new_password = request.POST.get('new_password')

            if not username or not new_password:
                messages.error(request, "يرجى تعبئة جميع الحقول المطلوبة")
            else:
                try:
                    # Check if admin exists
                    admin_ref = db.collection('admin').document(username)
                    admin_doc = admin_ref.get()

                    if admin_doc.exists:
                        # Update the admin password
                        hashed_password = generate_hashed_password(new_password)
                        admin_ref.update({'password': hashed_password})
                        messages.success(request, f"تم تغيير كلمة المرور للمشرف {username} بنجاح")
                    else:
                        messages.error(request, "المشرف غير موجود")
                except Exception as e:
                    messages.error(request, f"حدث خطأ أثناء تغيير كلمة المرور: {str(e)}")

        elif action == 'create_admin':
            username = request.POST.get('username')
            password = request.POST.get('password')

            if not username or not password:
                messages.error(request, "يرجى تعبئة جميع الحقول المطلوبة")
            else:
                try:
                    # Check if admin already exists
                    admin_ref = db.collection('admin').document(username)
                    admin_doc = admin_ref.get()

                    if admin_doc.exists:
                        messages.error(request, "اسم المستخدم موجود بالفعل")
                    else:
                        # Create new admin
                        hashed_password = generate_hashed_password(password)
                        admin_ref.set({'password': hashed_password})
                        messages.success(request, f"تم إنشاء المشرف {username} بنجاح")
                except Exception as e:
                    messages.error(request, f"حدث خطأ أثناء إنشاء المشرف: {str(e)}")

        elif action == 'set_manager_password':
            manager_password = request.POST.get('manager_password')

            if not manager_password:
                messages.error(request, "يرجى إدخال كلمة المرور")
            else:
                try:
                    # Hash the manager password and store it in the database
                    hashed_password = generate_hashed_password(manager_password)
                    db.collection('system_settings').document('manager_password').set({
                        'password': hashed_password,
                        'updated_at': firestore.SERVER_TIMESTAMP
                    })
                    has_manager_password = True
                    messages.success(request, "تم تعيين كلمة مرور المدير بنجاح")
                except Exception as e:
                    messages.error(request, f"حدث خطأ أثناء تعيين كلمة مرور المدير: {str(e)}")

        elif action == 'delete_admin':
            username = request.POST.get('username')

            if not username:
                messages.error(request, "يرجى تحديد المشرف المراد حذفه")
            else:
                try:
                    # Check if admin exists
                    admin_ref = db.collection('admin').document(username)
                    admin_doc = admin_ref.get()

                    if admin_doc.exists:
                        # Delete the admin
                        admin_ref.delete()
                        messages.success(request, f"تم حذف المشرف {username} بنجاح")
                    else:
                        messages.error(request, "المشرف غير موجود")
                except Exception as e:
                    messages.error(request, f"حدث خطأ أثناء حذف المشرف: {str(e)}")

    # Get all admin users
    admins = []
    try:
        admin_docs = db.collection('admin').stream()
        for doc in admin_docs:
            admins.append(doc.id)
    except Exception as e:
        messages.error(request, f"حدث خطأ أثناء جلب قائمة المشرفين: {str(e)}")

    return render(request, 'KLC_App/admin/admin_management.html', {
        'active_page': 'admin_management',
        'admins': admins,
        'has_manager_password': has_manager_password
    })

def export_data(request):
    try:
        # Get all users from Firestore
        users_ref = db.collection('persons')
        docs = users_ref.stream()

        # Prepare data
        data = []
        for doc in docs:
            user = doc.to_dict()
            data.append({
                'الاسم': user.get('name', 'غير معروف'),
                'المتبقي لنهاية 2024': user.get('debts_amount_2024', 0),
                'رقم الهوية': user.get('national_id', '')
            })

        # Create DataFrame with correct column order
        df = pd.DataFrame(data, columns=[
            'الاسم',
            'المتبقي لنهاية 2024',
            'رقم الهوية'
        ])

        # Create Excel file in memory
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer,
                      sheet_name='CleanedData',
                      index=False,
                      startrow=1,
                      header=False)

            # Add Arabic headers manually
            worksheet = writer.sheets['CleanedData']
            worksheet.cell(row=1, column=1, value="الاسم")
            worksheet.cell(row=1, column=2, value="المتبقي لنهاية 2024")
            worksheet.cell(row=1, column=3, value="رقم الهوية")

        # Prepare response
        output.seek(0)
        response = HttpResponse(
            output.getvalue(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="proper_data_export.xlsx"'
        return response

    except Exception as e:
        return HttpResponse(f"Error exporting data: {str(e)}", status=500)

def custom_404(request, exception):
    return render(request, 'KLC_App/404.html', status=404)

def custom_500(request):
    return render(request, 'KLC_App/500.html', status=500)

def serve_media_file(request, path):
    """
    Custom view to serve media files from different locations, handling errors gracefully. This function tries different paths for the file and returns a response with the file contents and appropriate headers. This is a fallback view that can be used in all environments.
    """
    # Normalize the path to handle different formats and remove leading slashes and backslashes
    path = path.replace('\\', '/').lstrip('/').lstrip('\\')

    # Try different path combinations to find the file, starting with the most likely location first
    possible_paths = [
        os.path.join(settings.MEDIA_ROOT, path),  # New path for media files in the new version of the site
        os.path.join(settings.BASE_DIR, 'static', 'media', path),  # Legacy path for static media in the old version of the site
        os.path.join(settings.BASE_DIR, 'media', path),  # Legacy path for media in the old version of the site
        os.path.join(settings.BASE_DIR, path),  # Legacy path for base directory in the old version of the site
        os.path.join(os.path.dirname(settings.BASE_DIR), 'media', path),  # Legacy path for parent media directory in the old version of the site
        os.path.join(settings.BASE_DIR, 'static', 'images', path),  # Legacy path for static images in the old version of the site
    ]

    # Try each possible path in order
    for file_path in possible_paths:
        # Check if the file exists and is a regular file
        if os.path.exists(file_path) and os.path.isfile(file_path):
            try:
                # Guess the content type based on the file extension
                content_type, encoding = mimetypes.guess_type(file_path)
                content_type = content_type or 'application/octet-stream'

                # Open the file in binary mode
                with open(file_path, 'rb') as f:
                    # Create a response with the file contents and appropriate content type
                    response = HttpResponse(f.read(), content_type=content_type)

                # Set the Content-Disposition header to inline for viewing in browser
                response['Content-Disposition'] = f'inline; filename="{os.path.basename(file_path)}"'

                # Add cache control headers to prevent caching issues
                response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'
                response['Pragma'] = 'no-cache'
                response['Expires'] = '0'

                # Return the response with the file contents and appropriate headers
                return response
            except Exception as e:
                # Log the error but continue trying other paths
                print(f"Error serving file {file_path}: {str(e)}")
                continue

    # If we get here, the file wasn't found in any of the possible locations
    # Return a 404 response with a custom message
    return HttpResponse(f"File not found: {path}", status=404)

def generate_hashed_password(plain_password):
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(plain_password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def create_admin_user(username, plain_password):
    hashed_password = generate_hashed_password(plain_password)
    db.collection('admin').document(username).set({
        'password': hashed_password
    })
    print(f"Admin user '{username}' created successfully!")

# admin creation
# create_admin_user('ahmad', 'ahmad')

# News Management Views
def admin_news(request):
    # Get all news from Firestore
    news_ref = db.collection('news_achievements').stream()
    news_list = []

    for doc in news_ref:
        data = doc.to_dict()
        data['id'] = doc.id

        # Process image URLs for display
        data = process_news_for_display(data)

        news_list.append(data)

    # Sort by date descending
    news_list.sort(key=lambda x: x.get('published_at', ''), reverse=True)

    context = {
        'news_list': news_list,
        'active_page': 'news',
    }

    return render(request, 'KLC_App/admin/admin_news.html', context)

def add_news(request):
    if request.method == 'POST':
        form = NewsForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                # Generate a unique ID for the news
                news_id = str(uuid.uuid4())

                # Handle main image upload
                image_urls = None
                if 'image' in request.FILES:
                    image = request.FILES['image']
                    # Upload the image to Firebase Storage first, then fall back to local storage
                    image_filename = f"news_{news_id}_main"
                    image_urls = upload_to_firebase_storage(
                        image,
                        folder='news_images',
                        filename=image_filename
                    )

                    # Check if there was an error uploading the image
                    if image_urls.get('error'):
                        raise Exception(f"Failed to upload main image: {image_urls.get('error')}")

                    # Check if at least one storage method succeeded
                    if not image_urls.get('firebase_url') and not image_urls.get('local_url'):
                        raise Exception("Failed to upload main image to any storage location")

                # Handle additional images upload
                additional_images = []
                failed_additional_images = 0
                if form.cleaned_data['additional_images']:
                    # Get the files from the cleaned data - it's already a list from our updated MultipleFileField
                    files = form.cleaned_data['additional_images']

                    # Debug information
                    print(f"Additional images: {len(files)} files")
                    print(f"Files type: {type(files)}")

                    for i, img in enumerate(files):
                        try:
                            # Upload each additional image to Firebase Storage first, then fall back to local storage
                            img_filename = f"news_{news_id}_additional_{i+1}"
                            img_urls = upload_to_firebase_storage(
                                img,
                                folder='news_images',
                                filename=img_filename
                            )

                            # Check if there was an error uploading the image
                            if img_urls.get('error'):
                                print(f"Failed to upload additional image {i+1}: {img_urls.get('error')}")
                                failed_additional_images += 1
                                continue

                            # Check if at least one storage method succeeded
                            if img_urls.get('firebase_url') or img_urls.get('local_url'):
                                additional_images.append(img_urls)
                                # Debug information
                                print(f"Saved additional image {i+1}: {img_urls}")
                            else:
                                print(f"Failed to upload additional image {i+1}")
                                failed_additional_images += 1
                        except Exception as e:
                            print(f"Error saving additional image {i+1}: {str(e)}")
                            failed_additional_images += 1

                # Debug information
                print(f"Final additional_images list: {additional_images}")

                # Warn if some additional images failed to upload
                if failed_additional_images > 0:
                    print(f"Warning: {failed_additional_images} additional images failed to upload")

                # Create news data
                news_data = {
                    'title': form.cleaned_data['title'],
                    'Description': form.cleaned_data['description'],
                    'image_src': image_urls,
                    'additional_images': additional_images,  # Add the list of additional images
                    'video_url': form.cleaned_data['video_url'] if form.cleaned_data['video_url'] else '',
                    'type': form.cleaned_data['news_type'],
                    'published_at': datetime.now().strftime('%Y-%m-%d'),
                    'is_featured': form.cleaned_data['is_featured'],
                }

                # If this news is featured, unfeature all other news
                if news_data['is_featured']:
                    # Get all news
                    all_news_ref = db.collection('news_achievements').stream()
                    for news_doc in all_news_ref:
                        if news_doc.id != news_id and news_doc.to_dict().get('is_featured', False):
                            # Unfeature this news
                            db.collection('news_achievements').document(news_doc.id).update({'is_featured': False})

                # Save to Firestore
                db.collection('news_achievements').document(news_id).set(news_data)

                # Show success message, with a warning if some additional images failed
                if failed_additional_images > 0:
                    messages.success(request, f"تم إضافة الخبر بنجاح، ولكن فشل تحميل {failed_additional_images} من الصور الإضافية")
                else:
                    messages.success(request, "تم إضافة الخبر بنجاح")

                return redirect('admin_news')

            except Exception as e:
                messages.error(request, f"حدث خطأ أثناء إضافة الخبر: {str(e)}")
    else:
        form = NewsForm()

    return render(request, 'KLC_App/admin/add_news.html', {'form': form, 'active_page': 'news'})

def edit_news(request, news_id):
    # Get the news from Firestore
    news_ref = db.collection('news_achievements').document(news_id)
    news_doc = news_ref.get()

    if not news_doc.exists:
        messages.error(request, "الخبر غير موجود")
        return redirect('admin_news')

    news_data = news_doc.to_dict()
    current_image = news_data.get('image_src', '')
    current_additional_images = news_data.get('additional_images', [])

    if request.method == 'POST':
        form = NewsForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                # Handle main image upload
                image_urls = current_image
                if 'image' in request.FILES:
                    image = request.FILES['image']

                    # Delete the old image from storage if it exists
                    if current_image:
                        delete_from_firebase_storage(current_image)

                    # Upload the new image to Firebase Storage first, then fall back to local storage
                    image_filename = f"news_{news_id}_main"
                    image_urls = upload_to_firebase_storage(
                        image,
                        folder='news_images',
                        filename=image_filename
                    )

                    # Check if there was an error uploading the image
                    if image_urls.get('error'):
                        raise Exception(f"Failed to upload main image: {image_urls.get('error')}")

                    # Check if at least one storage method succeeded
                    if not image_urls.get('firebase_url') and not image_urls.get('local_url'):
                        raise Exception("Failed to upload main image to any storage location")

                # Handle additional images upload
                additional_images = current_additional_images
                failed_additional_images = 0
                if form.cleaned_data['additional_images']:
                    # If new additional images are uploaded, delete the old ones from storage
                    for old_img_url in current_additional_images:
                        delete_from_firebase_storage(old_img_url)

                    # Replace with new images
                    additional_images = []
                    files = form.cleaned_data['additional_images']

                    # Debug information
                    print(f"Edit news - Additional images: {len(files)} files")
                    print(f"Edit news - Files type: {type(files)}")

                    for i, img in enumerate(files):
                        try:
                            # Upload each additional image to Firebase Storage first, then fall back to local storage
                            img_filename = f"news_{news_id}_additional_{i+1}"
                            img_urls = upload_to_firebase_storage(
                                img,
                                folder='news_images',
                                filename=img_filename
                            )

                            # Check if there was an error uploading the image
                            if img_urls.get('error'):
                                print(f"Edit news - Failed to upload additional image {i+1}: {img_urls.get('error')}")
                                failed_additional_images += 1
                                continue

                            # Check if at least one storage method succeeded
                            if img_urls.get('firebase_url') or img_urls.get('local_url'):
                                additional_images.append(img_urls)
                                # Debug information
                                print(f"Edit news - Saved additional image {i+1}: {img_urls}")
                            else:
                                print(f"Edit news - Failed to upload additional image {i+1}")
                                failed_additional_images += 1
                        except Exception as e:
                            print(f"Edit news - Error saving additional image {i+1}: {str(e)}")
                            failed_additional_images += 1

                # Debug information
                print(f"Edit news - Final additional_images list: {additional_images}")

                # Warn if some additional images failed to upload
                if failed_additional_images > 0:
                    print(f"Edit news - Warning: {failed_additional_images} additional images failed to upload")

                # Update news data
                updated_news_data = {
                    'title': form.cleaned_data['title'],
                    'Description': form.cleaned_data['description'],
                    'image_src': image_urls,
                    'additional_images': additional_images,  # Add the list of additional images
                    'video_url': form.cleaned_data['video_url'] if form.cleaned_data['video_url'] else '',
                    'type': form.cleaned_data['news_type'],
                    'published_at': news_data.get('published_at', datetime.now().strftime('%Y-%m-%d')),
                    'is_featured': form.cleaned_data['is_featured'],
                }

                # If this news is featured, unfeature all other news
                if updated_news_data['is_featured']:
                    # Get all news
                    all_news_ref = db.collection('news_achievements').stream()
                    for news_doc in all_news_ref:
                        if news_doc.id != news_id and news_doc.to_dict().get('is_featured', False):
                            # Unfeature this news
                            db.collection('news_achievements').document(news_doc.id).update({'is_featured': False})

                # Update in Firestore
                news_ref.update(updated_news_data)

                # Show success message, with a warning if some additional images failed
                if failed_additional_images > 0:
                    messages.success(request, f"تم تحديث الخبر بنجاح، ولكن فشل تحميل {failed_additional_images} من الصور الإضافية")
                else:
                    messages.success(request, "تم تحديث الخبر بنجاح")

                return redirect('admin_news')

            except Exception as e:
                messages.error(request, f"حدث خطأ أثناء تحديث الخبر: {str(e)}")
    else:
        # Pre-populate the form with existing data
        initial_data = {
            'title': news_data.get('title', ''),
            'description': news_data.get('Description', ''),
            'video_url': news_data.get('video_url', ''),
            'news_type': news_data.get('type', 'أخبار'),
            'is_featured': news_data.get('is_featured', False),
        }
        form = NewsForm(initial=initial_data)

    # Prepare current image URL for display
    display_image = None
    if isinstance(current_image, dict):
        # New format - prefer Firebase URL if available, otherwise use local URL
        display_image = current_image.get('firebase_url') or current_image.get('local_url')
    else:
        # Legacy format - use as is
        display_image = current_image

    # Prepare current additional images for display
    display_additional_images = []
    for img in current_additional_images:
        if isinstance(img, dict):
            # New format - prefer Firebase URL if available, otherwise use local URL
            display_img = img.get('firebase_url') or img.get('local_url')
            if display_img:
                display_additional_images.append(display_img)
        else:
            # Legacy format - use as is
            display_additional_images.append(img)

    context = {
        'form': form,
        'current_image': display_image,
        'current_additional_images': display_additional_images,
        'active_page': 'news',
    }

    return render(request, 'KLC_App/admin/edit_news.html', context)

def delete_news(request, news_id):
    try:
        # Get the news from Firestore
        news_ref = db.collection('news_achievements').document(news_id)
        news_doc = news_ref.get()

        if not news_doc.exists:
            messages.error(request, "الخبر غير موجود")
            return redirect('admin_news')

        # Get the image URLs to delete the files if needed
        news_data = news_doc.to_dict()
        image_url = news_data.get('image_src', '')
        additional_images = news_data.get('additional_images', [])

        # Delete from Firestore
        news_ref.delete()

        # Delete the main image if it exists
        if image_url:
            delete_from_firebase_storage(image_url)

        # Delete all additional images
        for img_url in additional_images:
            if img_url:
                delete_from_firebase_storage(img_url)

        messages.success(request, "تم حذف الخبر بنجاح")
    except Exception as e:
        messages.error(request, f"حدث خطأ أثناء حذف الخبر: {str(e)}")

    return redirect('admin_news')

@csrf_exempt
def delete_news_image(request, news_id):
    """
    AJAX endpoint to delete individual images from news
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    try:
        # Get the news from Firestore
        news_ref = db.collection('news_achievements').document(news_id)
        news_doc = news_ref.get()

        if not news_doc.exists:
            return JsonResponse({'success': False, 'error': 'الخبر غير موجود'}, status=404)

        news_data = news_doc.to_dict()
        image_type = request.POST.get('image_type')  # 'main' or 'additional'
        image_url = request.POST.get('image_url')

        if not image_type or not image_url:
            return JsonResponse({'success': False, 'error': 'معاملات مفقودة'}, status=400)

        if image_type == 'main':
            # Delete main image
            current_image = news_data.get('image_src', '')

            # Verify the image URL matches
            if isinstance(current_image, dict):
                current_url = current_image.get('firebase_url') or current_image.get('local_url')
            else:
                current_url = current_image

            if current_url != image_url:
                return JsonResponse({'success': False, 'error': 'رابط الصورة غير صحيح'}, status=400)

            # Delete from storage
            delete_from_firebase_storage(current_image)

            # Update Firestore - remove main image
            news_ref.update({'image_src': ''})

        elif image_type == 'additional':
            # Delete from additional images
            current_additional_images = news_data.get('additional_images', [])
            updated_additional_images = []
            image_found = False

            for img in current_additional_images:
                if isinstance(img, dict):
                    img_url = img.get('firebase_url') or img.get('local_url')
                else:
                    img_url = img

                if img_url == image_url:
                    # Delete from storage
                    delete_from_firebase_storage(img)
                    image_found = True
                else:
                    # Keep this image
                    updated_additional_images.append(img)

            if not image_found:
                return JsonResponse({'success': False, 'error': 'الصورة غير موجودة'}, status=404)

            # Update Firestore with remaining images
            news_ref.update({'additional_images': updated_additional_images})

        else:
            return JsonResponse({'success': False, 'error': 'نوع الصورة غير صحيح'}, status=400)

        return JsonResponse({'success': True, 'message': 'تم حذف الصورة بنجاح'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ أثناء حذف الصورة: {str(e)}'}, status=500)

def transaction_statistics(request):
    # Get date range from request
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    # جميع أنواع المعاملات
    all_types = list(transaction_types.values())

    # Initialize query
    transactions_ref = db.collection('transactions')

    # Get all transactions
    transactions = transactions_ref.stream()

    # Initialize counters and lists
    total_transactions = 0
    transactions_by_type = {t: 0 for t in all_types}
    completed_transactions_by_type = {t: 0 for t in all_types}
    completed_transactions_list = []

    # Process transactions
    for transaction in transactions:
        transaction_data = transaction.to_dict()
        transaction_type = transaction_data.get('transaction_type', 'غير محدد')
        status = transaction_data.get('transaction_status', 'In Progress')
        completed_at = transaction_data.get('completed_at')

        # Count total transactions
        total_transactions += 1

        # Count by type
        if transaction_type not in transactions_by_type:
            transactions_by_type[transaction_type] = 0
        transactions_by_type[transaction_type] += 1

        # Count completed transactions by type and collect completed transactions
        if status == 'Done':
            if transaction_type not in completed_transactions_by_type:
                completed_transactions_by_type[transaction_type] = 0
            completed_transactions_by_type[transaction_type] += 1

            # Add to completed transactions list
            transaction_data['id'] = transaction.id
            completed_transactions_list.append(transaction_data)

    # حساب النسبة المئوية لكل نوع معاملة
    percent_by_type = {}
    for t in all_types:
        total = transactions_by_type.get(t, 0)
        done = completed_transactions_by_type.get(t, 0)
        if total > 0:
            percent = int(round((done / total) * 100))
        else:
            percent = 0
        percent_by_type[t] = percent

    # Filter completed transactions by date if dates are provided
    if start_date and end_date:
        try:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            end_datetime = datetime.strptime(end_date, '%Y-%m-%d')

            filtered_completed_transactions = []
            for transaction in completed_transactions_list:
                completed_at = transaction.get('completed_at')
                if completed_at:
                    try:
                        # Try different date formats
                        try:
                            trans_date = datetime.strptime(completed_at, '%Y-%m-%d at %H:%M:%S')
                        except ValueError:
                            try:
                                trans_date = datetime.strptime(completed_at, '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                continue

                        # Include transactions on the end date by setting end time to 23:59:59
                        end_datetime_inclusive = datetime(end_datetime.year, end_datetime.month, end_datetime.day, 23, 59, 59)
                        if start_datetime <= trans_date <= end_datetime_inclusive:
                            filtered_completed_transactions.append(transaction)
                    except ValueError:
                        continue

            completed_transactions_list = filtered_completed_transactions
        except ValueError:
            pass

    # Sort completed transactions by completion date (newest first)
    def get_completion_date(transaction):
        completed_at = transaction.get('completed_at')
        if not completed_at:
            return datetime.min

        try:
            return datetime.strptime(completed_at, '%Y-%m-%d at %H:%M:%S')
        except ValueError:
            try:
                return datetime.strptime(completed_at, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                return datetime.min

    completed_transactions_list.sort(key=get_completion_date, reverse=True)

    context = {
        'total_transactions': total_transactions,
        'transactions_by_type': transactions_by_type,
        'completed_transactions_by_type': completed_transactions_by_type,
        'percent_by_type': percent_by_type,
        'transactions': completed_transactions_list,
        'all_types': all_types,
        'start_date': start_date,
        'end_date': end_date,
        'active_page': 'statistics',
    }

    return render(request, 'KLC_App/admin/transaction_statistics.html', context)
